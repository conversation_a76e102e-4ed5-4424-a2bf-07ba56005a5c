const functions = require("firebase-functions/v2");
const admin = require("firebase-admin");
admin.initializeApp();

// Helper function to check if medication is expired
async function isMedicationExpired(expiryDate) {
  const istNowString = new Date().toLocaleString("en-US", {
    timeZone: "Asia/Kolkata",
  });
  const todayIST = new Date(istNowString);
  todayIST.setHours(0, 0, 0, 0);

  const expiry = parseDateIST(expiryDate);
  return expiry < todayIST.getTime();
}

// Helper function to send notification
async function sendNotification(phone, med, currentTime) {
  try {
    const userDoc = await admin.firestore().collection("users").doc(phone).get();
    const fcmToken = userDoc.exists ? userDoc.data().fcm_token : null;

    if (!fcmToken) {
      console.log(`No FCM token found for user: ${phone}`);
      return;
    }

    const message = {
      notification: {
        title: "Me<PERSON> Reminder",
        body: `Hi, it's time to take ${med.medicine_name}`,
      },
      data: {
        type: "medication_reminder",
        medication_name: med.medicine_name,
        time: currentTime,
        dosage: med.dosage || "",
        phone: phone,
      },
      android: {
        priority: "high",
        notification: {
          channel_id: "healo_notifications",
        },
      },
      token: fcmToken,
    };

    await admin.messaging().send(message);
    console.log(`Notification sent successfully to ${phone} for ${med.medicine_name}`);

    // Store notification history
    await storeNotificationHistory(phone, med, currentTime);

  } catch (error) {
    console.error(`Failed to send notification to ${phone}:`, error);

    // Handle invalid or expired FCM tokens
    if (error.code === 'messaging/registration-token-not-registered' ||
        error.code === 'messaging/invalid-registration-token') {
      console.log(`Clearing invalid FCM token for user: ${phone}`);
      try {
        // Clear the invalid FCM token from Firestore
        await admin.firestore().collection("users").doc(phone).update({
          fcm_token: admin.firestore.FieldValue.delete()
        });
        console.log(`Invalid FCM token cleared for user: ${phone}`);
      } catch (clearError) {
        console.error(`Error clearing invalid FCM token for ${phone}:`, clearError);
      }
    }
  }
}

// Helper function to store notification history
async function storeNotificationHistory(phone, med, currentTime) {
  try {
    const notificationId = `${phone}_${med.medicine_name}_${Date.now()}`;
    const now = new Date();

    const notificationData = {
      id: notificationId,
      type: "medication_reminder",
      title: "Medication Reminder",
      body: `Hi, it's time to take ${med.medicine_name}`,
      medication_name: med.medicine_name,
      time: currentTime,
      dosage: med.dosage || "",
      sent_at: admin.firestore.Timestamp.fromDate(now),
      status: "pending",
      action_taken_at: null,
      is_read: false,
    };

    await admin.firestore().collection("notification_history").doc(phone).set({
      notifications: admin.firestore.FieldValue.arrayUnion(notificationData)
    }, { merge: true });

    console.log(`Notification history stored for ${phone}: ${med.medicine_name}`);
  } catch (error) {
    console.error(`Failed to store notification history for ${phone}:`, error);
  }
}

// Helper function to check daily medications
async function checkDailyMedications(currentTime) {
  let notificationsSent = 0;
  try {
    const snapshot = await admin.firestore().collection("daily_medication").get();
    console.log(`Found ${snapshot.docs.length} daily medication documents`);

    for (const doc of snapshot.docs) {
      const phone = doc.id;
      const data = doc.data();

      if (!data.medicines) {
        console.log(`No daily medicines found for user: ${phone}`);
        continue;
      }

      for (const med of data.medicines) {
        if (
          med.frequency === "Daily" &&
          med.timing &&
          med.timing.includes(currentTime)
        ) {
          console.log(`Found matching daily medication for ${phone}: ${med.medicine_name}`);

          if (await isMedicationExpired(med.expiry_date)) {
            console.log(`Daily medication expired for ${phone}: ${med.medicine_name}`);
            continue;
          }

          await sendNotification(phone, med, currentTime);
          notificationsSent++;
        }
      }
    }
  } catch (error) {
    console.error("Error checking daily medications:", error);
  }
  return notificationsSent;
}

// Helper function to check weekly medications
async function checkWeeklyMedications(currentTime, currentDayName) {
  let notificationsSent = 0;
  try {
    const snapshot = await admin.firestore().collection("weekly_medication").get();
    console.log(`Found ${snapshot.docs.length} weekly medication documents`);

    for (const doc of snapshot.docs) {
      const phone = doc.id;
      const data = doc.data();

      if (!data.medicines) {
        console.log(`No weekly medicines found for user: ${phone}`);
        continue;
      }

      for (const med of data.medicines) {
        if (
          med.frequency === "Weekly" &&
          med.timing &&
          med.timing.includes(currentTime) &&
          med.days &&
          med.days.includes(currentDayName)
        ) {
          console.log(`Found matching weekly medication for ${phone}: ${med.medicine_name} on ${currentDayName}`);

          if (await isMedicationExpired(med.expiry_date)) {
            console.log(`Weekly medication expired for ${phone}: ${med.medicine_name}`);
            continue;
          }

          await sendNotification(phone, med, currentTime);
          notificationsSent++;
        }
      }
    }
  } catch (error) {
    console.error("Error checking weekly medications:", error);
  }
  return notificationsSent;
}

// Helper function to check monthly medications
async function checkMonthlyMedications(currentTime, currentDate, currentMonth, currentYear) {
  let notificationsSent = 0;
  try {
    const snapshot = await admin.firestore().collection("monthly_medication").get();
    console.log(`Found ${snapshot.docs.length} monthly medication documents`);

    for (const doc of snapshot.docs) {
      const phone = doc.id;
      const data = doc.data();

      if (!data.medicines) {
        console.log(`No monthly medicines found for user: ${phone}`);
        continue;
      }

      for (const med of data.medicines) {
        if (
          med.frequency === "Monthly" &&
          med.timing &&
          med.timing.includes(currentTime) &&
          med.dates &&
          isDateMatch(med.dates, currentDate, currentMonth, currentYear)
        ) {
          console.log(`Found matching monthly medication for ${phone}: ${med.medicine_name} on date ${currentDate}`);

          if (await isMedicationExpired(med.expiry_date)) {
            console.log(`Monthly medication expired for ${phone}: ${med.medicine_name}`);
            continue;
          }

          await sendNotification(phone, med, currentTime);
          notificationsSent++;
        }
      }
    }
  } catch (error) {
    console.error("Error checking monthly medications:", error);
  }
  return notificationsSent;
}

// Helper function to check if current date matches any of the stored dates
function isDateMatch(storedDates, currentDate, currentMonth, currentYear) {
  for (const storedDate of storedDates) {
    if (!storedDate) continue;

    // Check if it's just a day number (legacy format)
    if (storedDate === currentDate) {
      console.log(`Date match found: ${storedDate} matches current date ${currentDate}`);
      return true;
    }

    // Check if it's a full date format (day/month/year)
    if (storedDate.includes('/')) {
      const [day, month, year] = storedDate.split('/');
      if (day === currentDate && month === currentMonth.toString() && year === currentYear.toString()) {
        console.log(`Full date match found: ${storedDate} matches current date ${currentDate}/${currentMonth}/${currentYear}`);
        return true;
      }
    }
  }
  return false;
}

exports.sendDailyMedicationReminders = functions.scheduler.onSchedule({
  schedule: "every 1 minutes",
  timeZone: "Asia/Kolkata",
  region: "asia-south1",
}, async (event) => {
  try {
    console.log("Starting medication reminder check...");

    const istNowString = new Date().toLocaleString("en-US", {
      timeZone: "Asia/Kolkata",
    });
    const istNow = new Date(istNowString);

    const currentTime = istNow.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    // Get current day name and date for weekly/monthly checks
    const currentDayName = istNow.toLocaleDateString("en-US", { weekday: "long" });
    const currentDate = istNow.getDate().toString();
    const currentMonth = istNow.getMonth() + 1; // getMonth() returns 0-11, so add 1
    const currentYear = istNow.getFullYear();

    console.log(`Current IST time: ${currentTime}`);
    console.log(`Current day: ${currentDayName}`);
    console.log(`Current date: ${currentDate}`);
    console.log(`Current month: ${currentMonth}`);
    console.log(`Current year: ${currentYear}`);

    let totalNotificationsSent = 0;

    // Check daily medications
    const dailyNotifications = await checkDailyMedications(currentTime);
    totalNotificationsSent += dailyNotifications;

    // Check weekly medications
    const weeklyNotifications = await checkWeeklyMedications(currentTime, currentDayName);
    totalNotificationsSent += weeklyNotifications;

    // Check monthly medications
    const monthlyNotifications = await checkMonthlyMedications(currentTime, currentDate, currentMonth, currentYear);
    totalNotificationsSent += monthlyNotifications;

    console.log(`Medication reminder check completed. Sent ${totalNotificationsSent} notifications.`);
    return { success: true, notificationsSent: totalNotificationsSent };

  } catch (error) {
    console.error("Error in medication reminder function:", error);
    throw error;
  }
});

function parseDateIST(dateStr) {
  const [day, month, year] = dateStr.split("/").map((s) => parseInt(s));
  const date = new Date(Date.UTC(year, month - 1, day)); // UTC base
  const istDate = new Date(date.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  istDate.setHours(0, 0, 0, 0);
  return istDate.getTime();
}