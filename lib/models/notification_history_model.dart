import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationHistory {
  final String id;
  final String type; // 'medication_reminder', 'health_alert', etc.
  final String title;
  final String body;
  final String medicationName;
  final String time; // Scheduled time (e.g., "08:00")
  final String dosage;
  final DateTime sentAt; // When notification was sent
  final String status; // 'pending', 'taken', 'skipped', 'missed'
  final DateTime? actionTakenAt; // When user took action
  final bool isRead; // Whether user has seen this notification

  NotificationHistory({
    required this.id,
    required this.type,
    required this.title,
    required this.body,
    required this.medicationName,
    required this.time,
    required this.dosage,
    required this.sentAt,
    this.status = 'pending',
    this.actionTakenAt,
    this.isRead = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'body': body,
      'medication_name': medicationName,
      'time': time,
      'dosage': dosage,
      'sent_at': Timestamp.fromDate(sentAt),
      'status': status,
      'action_taken_at': actionTakenAt != null ? Timestamp.fromDate(actionTakenAt!) : null,
      'is_read': isRead,
    };
  }

  factory NotificationHistory.fromMap(String id, Map<String, dynamic> map) {
    return NotificationHistory(
      id: id,
      type: map['type'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      medicationName: map['medication_name'] ?? '',
      time: map['time'] ?? '',
      dosage: map['dosage'] ?? '',
      sentAt: (map['sent_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: map['status'] ?? 'pending',
      actionTakenAt: (map['action_taken_at'] as Timestamp?)?.toDate(),
      isRead: map['is_read'] ?? false,
    );
  }

  NotificationHistory copyWith({
    String? id,
    String? type,
    String? title,
    String? body,
    String? medicationName,
    String? time,
    String? dosage,
    DateTime? sentAt,
    String? status,
    DateTime? actionTakenAt,
    bool? isRead,
  }) {
    return NotificationHistory(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      body: body ?? this.body,
      medicationName: medicationName ?? this.medicationName,
      time: time ?? this.time,
      dosage: dosage ?? this.dosage,
      sentAt: sentAt ?? this.sentAt,
      status: status ?? this.status,
      actionTakenAt: actionTakenAt ?? this.actionTakenAt,
      isRead: isRead ?? this.isRead,
    );
  }

  String toJson() => json.encode(toMap());
  
  factory NotificationHistory.fromJson(String source) =>
      NotificationHistory.fromMap("", json.decode(source));

  /// Check if notification is actionable (can still take action)
  bool get isActionable {
    return status == 'pending' && _isWithinActionWindow();
  }

  /// Check if notification is within action window (e.g., within 2 hours of scheduled time)
  bool _isWithinActionWindow() {
    final now = DateTime.now();
    final scheduledDateTime = _getScheduledDateTime();
    final timeDifference = now.difference(scheduledDateTime).inHours.abs();
    return timeDifference <= 2; // Allow action within 2 hours
  }

  /// Get the scheduled date time for this notification
  DateTime _getScheduledDateTime() {
    final today = DateTime.now();
    final timeParts = time.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    
    return DateTime(today.year, today.month, today.day, hour, minute);
  }

  /// Get formatted time for display
  String get formattedTime {
    final timeParts = time.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);
    
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$displayHour:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get formatted sent date for display
  String get formattedSentDate {
    final now = DateTime.now();
    final difference = now.difference(sentAt);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${sentAt.day}/${sentAt.month}/${sentAt.year}';
    }
  }
}
