import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/notification_history_model.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/models/medication_intake_model.dart';
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// State class for notification history
class NotificationHistoryState {
  final List<NotificationHistory> notifications;
  final bool isLoading;
  final String? error;
  final int unreadCount;

  const NotificationHistoryState({
    this.notifications = const [],
    this.isLoading = false,
    this.error,
    this.unreadCount = 0,
  });

  NotificationHistoryState copyWith({
    List<NotificationHistory>? notifications,
    bool? isLoading,
    String? error,
    int? unreadCount,
  }) {
    return NotificationHistoryState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

/// Notification history provider notifier
class NotificationHistoryNotifier extends StateNotifier<NotificationHistoryState> {
  final FirestoreService _firestoreService;

  NotificationHistoryNotifier(this._firestoreService)
      : super(const NotificationHistoryState());

  /// Fetch all notification history
  Future<void> fetchNotificationHistory() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final notifications = await _firestoreService.getNotificationHistory();
      final unreadCount = await _firestoreService.getUnreadNotificationCount();

      state = state.copyWith(
        notifications: notifications,
        unreadCount: unreadCount,
        isLoading: false,
      );

      log('Notification history fetched: ${notifications.length} notifications');
    } catch (e) {
      log('Error fetching notification history: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Store new notification history
  Future<void> storeNotificationHistory(NotificationHistory notification) async {
    try {
      await _firestoreService.storeNotificationHistory(notification);

      // Update local state
      final updatedNotifications = [notification, ...state.notifications];
      final updatedUnreadCount = state.unreadCount + 1;

      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: updatedUnreadCount,
      );

      log('Notification history stored: ${notification.medicationName}');
    } catch (e) {
      log('Error storing notification history: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Update notification status and record medication intake
  Future<void> updateNotificationStatus(
      String notificationId, String status) async {
    try {
      // Update notification status in Firestore
      await _firestoreService.updateNotificationStatus(notificationId, status);

      // Find the notification and record medication intake
      final notification = state.notifications.firstWhere(
        (n) => n.id == notificationId,
        orElse: () => throw Exception('Notification not found'),
      );

      // Record medication intake
      final intake = MedicationIntake(
        medicationName: notification.medicationName,
        date: DateFormat('dd-MM-yyyy').format(DateTime.now()),
        time: notification.time,
        status: status,
        timestamp: Timestamp.now(),
      );

      await _firestoreService.recordMedicationIntake(intake);

      // Mark notification as read when action is taken
      await _firestoreService.markNotificationAsRead(notificationId);

      // Update local state
      final updatedNotifications = state.notifications.map((n) {
        if (n.id == notificationId) {
          return n.copyWith(
            status: status,
            actionTakenAt: DateTime.now(),
            isRead: true, // Mark as read when action is taken
          );
        }
        return n;
      }).toList();

      // Update unread count
      final updatedUnreadCount = updatedNotifications.where((n) => !n.isRead).length;

      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: updatedUnreadCount,
      );

      log('Notification status updated: $notificationId - $status');
    } catch (e) {
      log('Error updating notification status: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestoreService.markNotificationAsRead(notificationId);

      // Update local state
      final updatedNotifications = state.notifications.map((n) {
        if (n.id == notificationId) {
          return n.copyWith(isRead: true);
        }
        return n;
      }).toList();

      final updatedUnreadCount = state.unreadCount > 0 ? state.unreadCount - 1 : 0;

      state = state.copyWith(
        notifications: updatedNotifications,
        unreadCount: updatedUnreadCount,
      );

      log('Notification marked as read: $notificationId');
    } catch (e) {
      log('Error marking notification as read: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Get pending (actionable) notifications
  List<NotificationHistory> get pendingNotifications {
    return state.notifications.where((n) => n.isActionable).toList();
  }

  /// Get today's notifications
  List<NotificationHistory> get todaysNotifications {
    final today = DateTime.now();
    return state.notifications.where((n) {
      return n.sentAt.year == today.year &&
          n.sentAt.month == today.month &&
          n.sentAt.day == today.day;
    }).toList();
  }

  /// Get notifications grouped by date
  Map<String, List<NotificationHistory>> get notificationsByDate {
    final Map<String, List<NotificationHistory>> grouped = {};

    for (final notification in state.notifications) {
      final dateKey = notification.formattedSentDate;
      if (grouped[dateKey] == null) {
        grouped[dateKey] = [];
      }
      grouped[dateKey]!.add(notification);
    }

    return grouped;
  }

  /// Clear all notifications (for testing purposes)
  void clearNotifications() {
    state = const NotificationHistoryState();
  }

  /// Refresh notification history
  Future<void> refresh() async {
    await fetchNotificationHistory();
  }
}

/// Notification history provider
final notificationHistoryProvider = StateNotifierProvider<NotificationHistoryNotifier, NotificationHistoryState>(
  (ref) => NotificationHistoryNotifier(FirestoreService()),
);

/// Provider for pending notifications count
final pendingNotificationsCountProvider = Provider<int>((ref) {
  final notificationState = ref.watch(notificationHistoryProvider);
  return notificationState.notifications.where((n) => n.isActionable).length;
});

/// Provider for unread notifications count
final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notificationState = ref.watch(notificationHistoryProvider);
  return notificationState.unreadCount;
});

/// Provider for today's notifications
final todaysNotificationsProvider = Provider<List<NotificationHistory>>((ref) {
  final notificationState = ref.watch(notificationHistoryProvider);
  final today = DateTime.now();
  return notificationState.notifications.where((n) {
    return n.sentAt.year == today.year &&
        n.sentAt.month == today.month &&
        n.sentAt.day == today.day;
  }).toList();
});
